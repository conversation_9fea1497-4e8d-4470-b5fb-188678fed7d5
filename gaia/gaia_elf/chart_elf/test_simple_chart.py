#!/usr/bin/env python3
"""
Test script for simple_bubble_chart__svg function
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from simple_chart_elf import simple_bubble_chart__svg

# Test the function structure without dependencies
def test_function_structure():
    """Test that the function is properly defined"""
    try:
        from simple_chart_elf import simple_bubble_chart__svg
        print("✅ Function imported successfully")

        # Check function signature
        import inspect
        sig = inspect.signature(simple_bubble_chart__svg)
        print(f"✅ Function signature: {sig}")

        # Check docstring
        if simple_bubble_chart__svg.__doc__:
            print("✅ Function has docstring")
        else:
            print("⚠️  Function missing docstring")

        return True
    except Exception as e:
        print(f"❌ Error importing function: {e}")
        return False



def test_simple_bubble_chart(test_data):
    """Test the bubble chart generation with sample country/GDP data"""
    
    # Sample data: country, gdp_growth, profit_margin, market_cap

    print("Testing simple_bubble_chart__svg with country data...")
    print("Input data:")
    print(test_data)
    print("\n" + "="*50 + "\n")
    
    try:
        # Generate the SVG chart
        svg_result = simple_bubble_chart__svg(test_data)
        
        print("Chart generation completed!")
        print(f"SVG length: {len(svg_result)} characters")
        
        # Save the result to a file for inspection
        output_file = "test_bubble_chart.svg"
        with open(output_file, 'w') as f:
            f.write(svg_result)
        print(f"SVG saved to: {output_file}")
        
        # Check if it looks like valid SVG
        if svg_result.strip().startswith('<svg') and svg_result.strip().endswith('</svg>'):
            print("✅ Generated content appears to be valid SVG")
        else:
            print("⚠️  Generated content may not be valid SVG")
            print("First 200 characters:")
            print(svg_result[:200])
            
    except Exception as e:
        print(f"❌ Error during chart generation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data = """Country,GDP_Growth,Unemployment_Rate,Population_Millions
United States,2.3,3.7,331
China,6.1,4.1,1439
Germany,0.6,3.2,83
Japan,0.7,2.8,125
United Kingdom,1.4,4.0,67
France,1.8,8.5,68
India,4.2,7.1,1380
Brazil,1.1,11.9,215
Canada,1.9,5.7,38
Australia,2.2,5.1,26"""

    test_data="""
### 1. **CAR-T Cell Therapy** - 40.2% CAGR
- **Market Size**: $12.88 billion (2025) → $128.55 billion (2034)
- **Focus**: Chimeric Antigen Receptor T-cell therapies for cancer treatment
- **Key Drivers**: Oncology breakthroughs, expanding therapeutic indications

### 2. **Cell and Gene Therapy** - 24.5% CAGR
- **Market Size**: $8.07 billion (2025) → $74.03 billion (2034) [CDMO segment]
- **Focus**: Advanced cellular and genetic therapeutic approaches
- **Key Drivers**: RNA breakthroughs, oncology applications, manufacturing scale-up

### 3. **Precision Medicine** - 16.5% CAGR
- **Market Size**: $654.46 billion (2025) → $1.315 trillion (2034)
- **Focus**: Personalized treatment based on individual genetic profiles
- **Key Drivers**: Genomic testing demand, AI integration, personalized diagnostics

### 4. **Proteomics** - 12.94% CAGR
- **Market Size**: $31.41 billion (2025) → $94.7 billion (2035)
- **Focus**: Large-scale study of proteins and their functions
- **Key Drivers**: AI integration, personalized medicine, biotech expansion

### 5. **Synthetic Biology** - 10.7% CAGR
- **Market Size**: $24.58 billion (2025) → $148.93 billion (2033)
- **Focus**: Engineering biological systems for manufacturing and therapeutics
- **Key Drivers**: Biomanufacturing, sustainable production, gene editing advances

## 🚀 Special Mention: AI in Drug Discovery
- **Transformative Impact**: Expected to generate $350-410 billion annually for pharma by 2025
- **Current Market**: $1-1.7 billion (growing exponentially)
- **Key Drivers**: Accelerated drug development, reduced costs, improved success rates

## 📊 Market Context
- **Overall Biotech Market**: $1.55 trillion (2024) → $5.71 trillion (2034)
- **Overall Biotech CAGR**: 13.6-13.9%
- **VC Investment Trend**: Healthcare funding growing from 6.2% (2022) to 14.3% (2025)

"""

    test_simple_bubble_chart(test_data)
