import plotly.graph_objects as go
import pandas as pd
import numpy as np

# SPACING PARAMETERS - ADJUST THESE TO FIX LAYOUT
SPACING = {
    'margin_block': 180,        # Standardized margin block size for all margins
    'margin_left': 100,         # Left margin (slightly more for Y-axis labels)
    'margin_right': 60,         # Right margin
    'legend_y': 1.35,           # Legend vertical position (>1 is above plot)
    'legend_padding_bottom': 0.08,  # Reduced padding below legend
    'title_y': 0.92,            # Title vertical position
    'title_padding_left': 0.02, # Left padding for title/subtitle
    'source_y': -0.25,          # Source annotation position (more padding above)
    'source_padding_top': 0.05, # Additional padding above source
    'accent_rect_y': 1.4,       # Top accent rectangle position
    'chart_height': 800,        # Taller chart height
    'chart_width': 2250,         # Chart width
    'yaxis_tickpad': 10,        # Space between Y-axis labels and plot area
}

# BUBBLE CHART PARAMETERS
BUBBLE_PARAMS = {
    'size_multiplier': 0.8,     # Controls overall bubble size
    'min_size': 10,             # Minimum bubble size
    'max_size': 60,             # Maximum bubble size
    'opacity': 0.7,             # Bubble transparency
    'line_width': 2,            # Bubble border width
}

# Define sophisticated color palette
sophisticated_colors = {
    'charcoal': '#212529',
    'slate': '#495057',
    'steel': '#6c757d',
    'sage': '#7a8b74',
    'clay': '#9b7d6b',
    'ochre': '#b89968',
    'dusty_blue': '#6b7a8b',
    'mauve': '#8b6b7a',
    'text_primary': '#212529',
    'text_secondary': '#6c757d',
    'border': '#ced4da',
    'grid': '#e9ecef',
    'background': '#f4f5ef',
    'highlight': '#fcf8e3',
    'emphasis': '#000000',
    'white': '#ffffff'
}

# Create sample bubble chart data
np.random.seed(42)
n_points = 25

# Create different categories
categories = ['Technology', 'Healthcare', 'Finance', 'Energy', 'Consumer']
data = pd.DataFrame({
    'x_metric': np.random.normal(50, 20, n_points),  # Revenue Growth %
    'y_metric': np.random.normal(15, 8, n_points),   # Profit Margin %
    'size_metric': np.random.exponential(30, n_points) + 10,  # Market Cap (billions)
    'category': np.random.choice(categories, n_points),
    'company': [f'Company {i+1}' for i in range(n_points)]
})

# Ensure positive values and reasonable ranges
data['x_metric'] = np.clip(data['x_metric'], -10, 100)
data['y_metric'] = np.clip(data['y_metric'], -5, 35)
data['size_metric'] = np.clip(data['size_metric'], 5, 150)

# Create figure
fig = go.Figure()

# Color mapping for categories
category_colors = {
    'Technology': sophisticated_colors['charcoal'],
    'Healthcare': sophisticated_colors['sage'],
    'Finance': sophisticated_colors['dusty_blue'],
    'Energy': sophisticated_colors['ochre'],
    'Consumer': sophisticated_colors['clay']
}

# Add bubble traces for each category
for category in categories:
    category_data = data[data['category'] == category]

    fig.add_trace(go.Scatter(
        x=category_data['x_metric'],
        y=category_data['y_metric'],
        mode='markers',
        name=category,
        marker=dict(
            size=category_data['size_metric'] * BUBBLE_PARAMS['size_multiplier'],
            sizemode='diameter',
            sizemin=BUBBLE_PARAMS['min_size'],
            # Note: sizemax is not a valid Plotly parameter - use sizeref instead for scaling
            sizeref=2. * max(data['size_metric']) / (BUBBLE_PARAMS['max_size']**2),
            color=category_colors[category],
            opacity=BUBBLE_PARAMS['opacity'],
            line=dict(
                width=BUBBLE_PARAMS['line_width'],
                color=sophisticated_colors['white']
            )
        ),
        text=category_data['company'],
        hovertemplate=(
            '<b>%{text}</b><br>' +
            'Revenue Growth: %{x:.1f}%<br>' +
            'Profit Margin: %{y:.1f}%<br>' +
            'Market Cap: $%{marker.size:.0f}B<br>' +
            '<extra></extra>'
        )
    ))

# Update layout using SPACING parameters
fig.update_layout(
    title={
        'text': 'Corporate Performance Matrix<br><sup style="font-size: 16px; color: #6c757d;">Revenue growth vs. profitability by market capitalization</sup>',
        'x': SPACING['title_padding_left'],
        'xanchor': 'left',
        'y': SPACING['title_y'],
        'yanchor': 'top',
        'font': {
            'family': 'Arial, sans-serif',
            'size': 28,
            'color': sophisticated_colors['text_primary']
        }
    },

    plot_bgcolor=sophisticated_colors['white'],
    paper_bgcolor=sophisticated_colors['background'],

    legend=dict(
        x=SPACING['title_padding_left'],
        y=SPACING['legend_y'] - SPACING['legend_padding_bottom'],
        xanchor='left',
        yanchor='top',
        orientation='h',
        font=dict(
            family='Arial, sans-serif',
            size=16,
            color=sophisticated_colors['text_secondary']
        ),
        bgcolor='rgba(0,0,0,0)',
        bordercolor='rgba(0,0,0,0)',
        itemsizing='constant',
        itemwidth=40
    ),

    margin=dict(
        l=SPACING['margin_left'],
        r=SPACING['margin_right'],
        t=SPACING['margin_block'] + 40,
        b=SPACING['margin_block']
    ),

    font=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),

    hovermode='closest',
    width=SPACING['chart_width'],
    height=SPACING['chart_height']
)

# X-axis styling
fig.update_xaxes(
    title=dict(
        text='Revenue Growth (%)',
        font=dict(
            family='Arial, sans-serif',
            size=18,
            color=sophisticated_colors['text_primary']
        ),
        standoff=20
    ),
    showgrid=True,
    gridwidth=1,
    gridcolor=sophisticated_colors['grid'],
    showline=True,
    linewidth=2,
    linecolor=sophisticated_colors['text_primary'],
    tickfont=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),
    tickformat='.0f',
    ticks='outside',
    ticklen=8,
    zeroline=True,
    zerolinewidth=2,
    zerolinecolor=sophisticated_colors['text_primary']
)

# Y-axis styling
fig.update_yaxes(
    title=dict(
        text='Profit Margin (%)',
        font=dict(
            family='Arial, sans-serif',
            size=18,
            color=sophisticated_colors['text_primary']
        ),
        standoff=20
    ),
    showgrid=True,
    gridwidth=1,
    gridcolor=sophisticated_colors['grid'],
    showline=True,
    linewidth=2,
    linecolor=sophisticated_colors['text_primary'],
    tickfont=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),
    tickformat='.0f',
    zeroline=True,
    zerolinewidth=2,
    zerolinecolor=sophisticated_colors['text_primary'],
    ticks='outside',
    ticklen=8,
    ticksuffix='  '  # Add space to the right of Y-axis labels
)

# Add quadrant reference lines (optional - can be uncommented)
# Average values for reference
avg_x = data['x_metric'].mean()
avg_y = data['y_metric'].mean()

fig.add_vline(
    x=avg_x,
    line_width=1,
    line_dash="dash",
    line_color=sophisticated_colors['steel'],
    opacity=0.5,
    layer="below"
)

fig.add_hline(
    y=avg_y,
    line_width=1,
    line_dash="dash",
    line_color=sophisticated_colors['steel'],
    opacity=0.5,
    layer="below"
)

# Add source annotation
fig.add_annotation(
    text="Source: Internal Analytics Dashboard • Bubble size represents market capitalization",
    xref="paper",
    yref="paper",
    x=SPACING['title_padding_left'],
    y=SPACING['source_y'] - SPACING['source_padding_top'],
    showarrow=False,
    font=dict(
        family='Arial, sans-serif',
        size=14,
        color=sophisticated_colors['text_secondary'],
        style='italic'
    ),
    xanchor='left'
)

# Add quadrant labels (optional)
fig.add_annotation(
    text="High Growth<br>High Margin",
    x=data['x_metric'].max() * 0.85,
    y=data['y_metric'].max() * 0.85,
    showarrow=False,
    font=dict(
        family='Arial, sans-serif',
        size=12,
        color=sophisticated_colors['steel']
    ),
    bgcolor="rgba(255,255,255,0.8)",
    bordercolor=sophisticated_colors['grid'],
    borderwidth=1
)

fig.add_annotation(
    text="Low Growth<br>High Margin",
    x=data['x_metric'].min() * 0.5,
    y=data['y_metric'].max() * 0.85,
    showarrow=False,
    font=dict(
        family='Arial, sans-serif',
        size=12,
        color=sophisticated_colors['steel']
    ),
    bgcolor="rgba(255,255,255,0.8)",
    bordercolor=sophisticated_colors['grid'],
    borderwidth=1
)

# fig is done
# Now: render

import plotly.io as pio
pio.renderers.default = "svg"        # consistent everywhere
fig.update_layout(autosize=True)    # belt‑and‑suspenders


# Set reasonable dimensions just for export
fig.update_layout(
    width=1200,
    height=700,
    autosize=False
)
fig.write_image("chart3.svg", width=1200, height=700)
