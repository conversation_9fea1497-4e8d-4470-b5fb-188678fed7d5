import plotly.graph_objects as go
import pandas as pd
import numpy as np

# Use the same sophisticated color palette and spacing parameters
# These are assumed to be defined in previous cells
# sophisticated_colors = {...}
# SPACING = {...}

bar_data = pd.DataFrame({
    'country': ['United States', 'China', 'Japan', 'Germany', 'India', 'UK', 'France'],
    'value': [21.4, 14.3, 5.1, 3.8, 2.9, 2.8, 2.7]
})

fig_bar = go.Figure()

# Define colors for bars, using sophisticated_colors
# Trying a different sequence: charcoal, slate, sage, dusty_blue, clay, ochre, steel
colors = [
    sophisticated_colors['charcoal'],  # United States
    sophisticated_colors['slate'],     # China
    sophisticated_colors['sage'],      # Japan
    sophisticated_colors['dusty_blue'],# Germany
    sophisticated_colors['clay'],      # India
    sophisticated_colors['ochre'],     # UK
    sophisticated_colors['steel']      # France
]


fig_bar.add_trace(go.Bar(
    x=bar_data['country'],
    y=bar_data['value'],
    marker_color=colors,
    text=bar_data['value'],
    texttemplate='%{text:.1f}',
    textposition='outside',
    textfont=dict(
        family='Arial, sans-serif', # Using Arial as in the sophisticated style
        size=18, # Slightly larger font for values
        color=sophisticated_colors['text_primary'], # Primary text color
        weight='bold'
    ),
    insidetextanchor='end' # Position text inside bars if needed
))

# Apply sophisticated layout styling
fig_bar.update_layout(
    title={
        'text': 'World\'s largest economies<br><sup style="font-size: 16px; color: {}">GDP in trillion dollars, 2023</sup>'.format(sophisticated_colors['text_secondary']),
        'x': SPACING['title_padding_left'],
        'xanchor': 'left',
        'y': SPACING['title_y'],
        'yanchor': 'top',
        'font': {
            'family': 'Arial, sans-serif',
            'size': 28,
            'color': sophisticated_colors['text_primary']
        }
    },
    plot_bgcolor=sophisticated_colors['white'],
    paper_bgcolor=sophisticated_colors['background'],
    margin=dict(
        l=SPACING['margin_left'],
        r=SPACING['margin_right'],
        t=SPACING['margin_block'] + 40, # Adjusted top margin
        b=SPACING['margin_block'] + 40  # Adjusted bottom margin for x-axis labels
    ),
    font=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),
    width=SPACING['chart_width'],
    height=SPACING['chart_height'],
    showlegend=False # Bar charts typically don't need a legend for single series
)

# X-axis styling
fig_bar.update_xaxes(
    showgrid=False,
    showline=True,
    linewidth=2,
    linecolor=sophisticated_colors['text_primary'],
    tickfont=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),
    tickangle=-45 # Keep angle for readability
)

# Y-axis styling
fig_bar.update_yaxes(
    showgrid=True,
    gridwidth=1,
    gridcolor=sophisticated_colors['grid'],
    showline=False, # No line on Y-axis in this style
    tickfont=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),
    range=[0, bar_data['value'].max() * 1.2], # Adjusted range for text above bars
    zeroline=True,
    zerolinewidth=2,
    zerolinecolor=sophisticated_colors['text_primary'],
    ticks='', # No ticks on Y-axis
    title='' # No Y-axis title
)

# Add source annotation
fig_bar.add_annotation(
    text="Source: World Bank", # Using World Bank as source for this data
    xref="paper",
    yref="paper",
    x=SPACING['title_padding_left'], # Align with title padding
    y=SPACING['source_y'] - SPACING['source_padding_top'], # Use spacing parameter
    showarrow=False,
    font=dict(
        family='Arial, sans-serif',
        size=14,
        color=sophisticated_colors['text_secondary'],
        style='italic'
    ),
    xanchor='left'
)

# Add subtle accent rectangle
fig_bar.add_shape(
    type="rect",
    xref="paper",
    yref="paper",
    x0=SPACING['title_padding_left'], # Align with title/legend
    y0=SPACING['accent_rect_y'],
    x1=SPACING['title_padding_left'] + 0.03, # Small width
    y1=SPACING['accent_rect_y'] + 0.02, # Small height
    fillcolor=sophisticated_colors['charcoal'], # Using charcoal for accent
    line=dict(width=0)
)

fig = fig_bar
#fig_bar.show()

# fig is done
# Now: render

import plotly.io as pio
pio.renderers.default = "svg"        # consistent everywhere
fig.update_layout(autosize=True)    # belt‑and‑suspenders


# Set reasonable dimensions just for export
fig.update_layout(
    width=1200,
    height=700,
    autosize=False
)
fig.write_image("chart3.svg", width=1200, height=700)
