import plotly.graph_objects as go
import pandas as pd
import numpy as np

# SPACING PARAMETERS - ADJUST THESE TO FIX LAYOUT
SPACING = {
    'margin_block': 180,        # Standardized margin block size for all margins
    'margin_left': 80,          # Left margin
    'margin_right': 60,         # Right margin
    'legend_y': 1.35,           # Legend vertical position (>1 is above plot)
    'legend_padding_bottom': 0.08,  # Reduced padding below legend
    'title_y': 0.92,            # Title vertical position
    'title_padding_left': 0.02, # Left padding for title/subtitle
    'source_y': -0.25,          # Source annotation position (more padding above)
    'source_padding_top': 0.05, # Additional padding above source
    'accent_rect_y': 1.4,       # Top accent rectangle position
    'chart_height': 700,        # Taller chart height
    'chart_width': 850,         # Chart width
    'yaxis_tickpad': 100,        # Space between Y-axis labels and plot area
}

# Define sophisticated color palette
sophisticated_colors = {
    'charcoal': '#212529',
    'slate': '#495057',
    'steel': '#6c757d',
    'sage': '#7a8b74',
    'clay': '#9b7d6b',
    'ochre': '#b89968',
    'dusty_blue': '#6b7a8b',
    'mauve': '#8b6b7a',
    'text_primary': '#212529',
    'text_secondary': '#6c757d',
    'border': '#ced4da',
    'grid': '#e9ecef',
    'background': '#f4f5ef',
    'highlight': '#fcf8e3',
    'emphasis': '#000000',
    'white': '#ffffff'
}

# Create sample data
np.random.seed(42)
dates = pd.date_range('2020-01-01', '2023-12-31', freq='M')
data = pd.DataFrame({
    'date': dates,
    'Series_1': 100 + np.cumsum(np.random.randn(len(dates)) * 0.5),
    'Series_2': 95 + np.cumsum(np.random.randn(len(dates)) * 0.7),
    'Series_3': 98 + np.cumsum(np.random.randn(len(dates)) * 0.4),
    'Series_4': 96 + np.cumsum(np.random.randn(len(dates)) * 0.6)
})

# Create figure
fig = go.Figure()

# Add traces
fig.add_trace(go.Scatter(
    x=data['date'],
    y=data['Series_1'],
    name='Primary Series',
    line=dict(color=sophisticated_colors['charcoal'], width=4),
    mode='lines'
))

fig.add_trace(go.Scatter(
    x=data['date'],
    y=data['Series_2'],
    name='Secondary Series',
    line=dict(color=sophisticated_colors['sage'], width=4),
    mode='lines'
))

fig.add_trace(go.Scatter(
    x=data['date'],
    y=data['Series_3'],
    name='Tertiary Series',
    line=dict(color=sophisticated_colors['dusty_blue'], width=4),
    mode='lines'
))

fig.add_trace(go.Scatter(
    x=data['date'],
    y=data['Series_4'],
    name='Quaternary Series',
    line=dict(color=sophisticated_colors['ochre'], width=4),
    mode='lines'
))

# Update layout using SPACING parameters
fig.update_layout(
    title={
        'text': 'Sophisticated Data Visualization<br><sup style="font-size: 16px; color: #6c757d;">Quarterly performance metrics, indexed</sup>',
        'x': SPACING['title_padding_left'],  # Added left padding
        'xanchor': 'left',
        'y': SPACING['title_y'],
        'yanchor': 'top',
        'font': {
            'family': 'Arial, sans-serif',
            'size': 28,
            'color': sophisticated_colors['text_primary']
        }
    },

    plot_bgcolor=sophisticated_colors['white'],
    paper_bgcolor=sophisticated_colors['background'],

    legend=dict(
        x=SPACING['title_padding_left'],  # Align legend with title padding
        y=SPACING['legend_y'] - SPACING['legend_padding_bottom'],  # Less padding below legend
        xanchor='left',
        yanchor='top',
        orientation='h',
        font=dict(
            family='Arial, sans-serif',
            size=16,
            color=sophisticated_colors['text_secondary']
        ),
        bgcolor='rgba(0,0,0,0)',
        bordercolor='rgba(0,0,0,0)',
        itemsizing='constant',
        itemwidth=40
    ),

    margin=dict(
        l=SPACING['margin_left'],
        r=SPACING['margin_right'],
        t=SPACING['margin_block'] + 40,  # Extra space for legend and title
        b=SPACING['margin_block']        # Standardized bottom marginSPACING = {
    'margin_block': 180,        # Standardized margin block size for all margins
    'margin_left': 80,          # Left margin
    'margin_right': 60,         # Right margin
    'legend_y': 1.35,           # Legend vertical position (>1 is above plot)
…
    ),

    font=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),

    hovermode='x unified',
    width=SPACING['chart_width'],
    height=SPACING['chart_height']  # Taller chart
)

# X-axis styling
fig.update_xaxes(
    showgrid=False,
    showline=True,
    linewidth=2,
    linecolor=sophisticated_colors['text_primary'],
    tickfont=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),
    tickformat='%Y',
    nticks=8,
    ticks='outside',
    ticklen=8,
    zeroline=False
)

# Y-axis styling
fig.update_yaxes(
    showgrid=True,
    gridwidth=1,
    gridcolor=sophisticated_colors['grid'],
    showline=False,
    tickfont=dict(
        family='Arial, sans-serif',
        size=16,
        color=sophisticated_colors['text_secondary']
    ),
    tickformat='.0f',
    zeroline=True,
    zerolinewidth=2,
    zerolinecolor=sophisticated_colors['text_primary'],
    ticks='',
    title='',
    ticksuffix='    '  # Add space to the right of Y-axis labels
)

# Add baseline
fig.add_hline(
    y=100,
    line_width=2,
    line_color=sophisticated_colors['text_primary'],
    layer="below"
)

# Add source annotation with more padding above
fig.add_annotation(
    text="Source: Internal Analytics Dashboard",
    xref="paper",
    yref="paper",
    x=SPACING['title_padding_left'],  # Align with title padding
    y=SPACING['source_y'] - SPACING['source_padding_top'],  # More padding above source
    showarrow=False,
    font=dict(
        family='Arial, sans-serif',
        size=14,
        color=sophisticated_colors['text_secondary'],
        style='italic'
    ),
    xanchor='left'
)

'''
# Add subtle accent rectangle
fig.add_shape(
    type="rect",
    xref="paper",
    yref="paper",
    x0=0,
    y0=SPACING['accent_rect_y'],
    x1=0.05,
    y1=SPACING['accent_rect_y'] + 0.02,
    fillcolor=sophisticated_colors['charcoal'],
    line=dict(width=0)
)
'''

# fig is done
# Now: render

import plotly.io as pio
pio.renderers.default = "svg"        # consistent everywhere
fig.update_layout(autosize=True)    # belt‑and‑suspenders


# Set reasonable dimensions just for export
fig.update_layout(
    width=1200,
    height=700,
    autosize=False
)
fig.write_image("chart3.svg", width=1200, height=700)
