#!/usr/bin/env python3
"""
Sprite: External BabyFetch (Web Scraping/Fetching)

Provides web scraping and content fetching capabilities using BabySpider.
This sprite allows fetching and parsing web content from URLs with efficient processing.
"""

import sys
import os
from typing import Dict, Any, Optional, List
import traceback

def babyfetch_url(url: str, max_text_length: int = 2000, include_html: bool = False, max_text_length: int = 2000) -> Dict[str, Any]:
    """
    Fetch and parse content from a URL using CachedBabyFetch.
    
    Args:
        url: The URL to fetch content from
        max_age: Maximum age in seconds for cached content (None = no age limit)
        refresh_cache: If True, bypass cache and fetch fresh content
    
    Returns:
        Dict containing:
        - success: bool - Whether the fetch was successful
        - url: str - The original URL
        - cache_hit: bool - Whether result came from cache
        - content: dict - Parsed content (html, text, markdown, etc.)
        - crawl_info: dict - Crawl metadata (status_code, headers, etc.)
        - error: str - Error message if failed
    """
    try:
        # Add parent directories to path for gaia imports
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)  # gaia/gaia_sprites -> gaia
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)

        # Import CachedBabyFetch
        from gaia.babyspider.cached_babyfetch import CachedBabyFetch
        from gaia.babyspider import babyparse
        
        # Create CachedBabyFetch instance
        cbf = CachedBabyFetch()
        
        # Fetch URL with caching
        result = cbf.fetch_url_or_get_cached(
            url=url,
            max_age=max_age,
            refresh_cache=refresh_cache
        )
        
        # Extract results
        cache_hit = result.get('cache_hit', False)
        results = result.get('results', {})
        
        if not results:
            return {
                'success': False,
                'url': url,
                'cache_hit': cache_hit,
                'error': 'No results returned from babyfetch'
            }
        
        # Get the protocol-specific result (usually 'https' or 'http')
        protocol = list(results.keys())[0]
        data = results[protocol]
        
        # Extract crawl information
        crawl_info = data.get('crawl', {})
        content_data = data.get('content', {})
        
        # Parse HTML content if available
        html = content_data.get('html', '')
        parsed_content = {}
        
        if html:
            try:
                # Extract text content
                parsed_content['text'] = babyparse.html_to_plain(html).strip()
                
                # Extract markdown
                parsed_content['markdown'] = babyparse.html_to_markdown(html)
                
                # Extract JavaScript
                parsed_content['scripts'] = babyparse.html_to_javascript(html)
                
                # Keep original HTML
                parsed_content['html'] = html
                
                # Extract basic metadata
                parsed_content['title'] = babyparse.extract_title(html) if hasattr(babyparse, 'extract_title') else ''
                parsed_content['word_count'] = len(parsed_content['text'].split())
                parsed_content['char_count'] = len(parsed_content['text'])
                
            except Exception as parse_error:
                parsed_content['parse_error'] = str(parse_error)
                parsed_content['html'] = html
                parsed_content['text'] = html[:1000] + '...' if len(html) > 1000 else html
        
        return {
            'success': True,
            'url': url,
            'cache_hit': cache_hit,
            'content': parsed_content,
            'crawl_info': {
                'status_code': crawl_info.get('status_code'),
                'headers': crawl_info.get('headers', {}),
                'final_url': crawl_info.get('final_url', url),
                'response_time': crawl_info.get('response_time'),
                'content_type': crawl_info.get('headers', {}).get('content-type', ''),
                'content_length': crawl_info.get('headers', {}).get('content-length', '')
            },
            'error': None
        }
        
    except Exception as e:
        return {
            'success': False,
            'url': url,
            'cache_hit': False,
            'error': f"BabyFetch error: {str(e)}",
            'traceback': traceback.format_exc()
        }

def babyfetch_multiple_urls(urls: List[str], max_age: int = None, refresh_cache: bool = False) -> Dict[str, Any]:
    """
    Fetch content from multiple URLs using CachedBabyFetch.
    
    Args:
        urls: List of URLs to fetch
        max_age: Maximum age in seconds for cached content
        refresh_cache: If True, bypass cache and fetch fresh content
    
    Returns:
        Dict containing:
        - success: bool - Overall success status
        - results: List[Dict] - Results for each URL
        - summary: Dict - Summary statistics
    """
    try:
        results = []
        success_count = 0
        cache_hit_count = 0
        
        for url in urls:
            result = babyfetch_url(url, max_age=max_age, refresh_cache=refresh_cache)
            results.append(result)
            
            if result['success']:
                success_count += 1
            if result.get('cache_hit', False):
                cache_hit_count += 1
        
        return {
            'success': True,
            'results': results,
            'summary': {
                'total_urls': len(urls),
                'successful_fetches': success_count,
                'failed_fetches': len(urls) - success_count,
                'cache_hits': cache_hit_count,
                'fresh_fetches': len(urls) - cache_hit_count,
                'success_rate': success_count / len(urls) if urls else 0,
                'cache_hit_rate': cache_hit_count / len(urls) if urls else 0
            }
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f"Multiple URL fetch error: {str(e)}",
            'traceback': traceback.format_exc()
        }

def babyfetch_clear_cache(url: str) -> Dict[str, Any]:
    """
    Clear cached content for a specific URL.
    
    Args:
        url: The URL to clear from cache
    
    Returns:
        Dict with success status and message
    """
    try:
        from gaia.babyspider.cached_babyfetch import CachedBabyFetch
        
        cbf = CachedBabyFetch()
        cbf.del_cache(url=url)
        
        return {
            'success': True,
            'message': f'Cache cleared for URL: {url}',
            'url': url
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f"Cache clear error: {str(e)}",
            'url': url
        }

# Test function
def test_babyfetch():
    """Test the babyfetch functionality"""
    print("🧪 Testing BabyFetch Sprite")
    print("=" * 40)
    
    # Test single URL fetch
    test_url = "https://www.agfunder.com/"
    print(f"Testing URL: {test_url}")
    
    result = babyfetch_url(test_url)
    print(f"Success: {result['success']}")
    print(f"Cache hit: {result.get('cache_hit', False)}")
    
    if result['success']:
        content = result['content']
        print(f"Content length: {content.get('char_count', 0)} chars")
        print(f"Word count: {content.get('word_count', 0)} words")
        print(f"Title: {content.get('title', 'N/A')}")
        print(f"Status code: {result['crawl_info'].get('status_code', 'N/A')}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    test_babyfetch()
