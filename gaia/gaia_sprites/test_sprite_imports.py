#!/usr/bin/env python3
"""
Test script to debug sprite imports in MCP server context
"""

import sys
import os

print("=== Debugging Sprite Imports ===")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")
print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries

# Add parent directory to path for sprite imports (same as MCP server)
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)
    print(f"Added to path: {parent_dir}")

print("\n=== Testing Individual Sprite Imports ===")

def test_import(module_name, func_name=None):
    try:
        if func_name:
            module = __import__(module_name, fromlist=[func_name])
            func = getattr(module, func_name)
            print(f"✅ {module_name}.{func_name}")
            return True
        else:
            __import__(module_name)
            print(f"✅ {module_name}")
            return True
    except Exception as e:
        print(f"❌ {module_name}{('.' + func_name) if func_name else ''}: {e}")
        return False

# Test all sprites used in MCP server
sprites_to_test = [
    ('spr_agf_agsearch', 'quick_search'),
    ('spr_tamarind', 'market_research'),
    ('spr_ext_search', 'web_search'),
    ('spr_ext_search', 'news_search'),
    ('spr_agf_omnisearch', 'omni_search'),
    ('spr_ext_wiki', 'wiki_search'),
    ('spr_agf_multimodel', 'multimodel_query'),
    ('spr_agf_frames', 'frame_search'),
    ('spr_agf_frames', 'frame_list'),
    ('spr_agf_investors', 'investor_search'),
    ('spr_agf_investors', 'investor_stats'),
    ('spr_llm_smart', 'llm_completion_text'),
    ('spr_llm_smart', 'llm_completion_json'),
    ('spr_llm_smart', 'llm_analyze_code'),
    ('spr_llm_smart', 'llm_generate_code'),
    ('spr_llm_smart', 'llm_explain_concept'),
    ('spr_llm_smart', 'llm_debug_error'),

]

success_count = 0
for module_name, func_name in sprites_to_test:
    if test_import(module_name, func_name):
        success_count += 1

print(f"\n=== Results ===")
print(f"Successfully imported: {success_count}/{len(sprites_to_test)}")

if success_count == len(sprites_to_test):
    print("🎉 All sprite imports successful!")
    
    # Test the exact import pattern from MCP server
    print("\n=== Testing MCP Server Import Pattern ===")
    try:
        from spr_agf_agsearch import quick_search
        from spr_tamarind import market_research
        from spr_ext_search import web_search, news_search
        from spr_agf_omnisearch import omni_search
        from spr_ext_wiki import wiki_search
        from spr_agf_multimodel import multimodel_query
        from spr_agf_frames import frame_search, frame_list
        from spr_agf_investors import investor_search, investor_stats
        from spr_llm_smart import (
            llm_completion_text, llm_completion_json, llm_analyze_code,
            llm_generate_code, llm_explain_concept, llm_debug_error
        )

        
        sprites = {
            'quick_search': quick_search,
            'market_research': market_research,
            'web_search': web_search,
            'news_search': news_search,
            'omni_search': omni_search,
            'wiki_search': wiki_search,
            'multimodel_query': multimodel_query,
            'frame_search': frame_search,
            'frame_list': frame_list,
            'investor_search': investor_search,
            'investor_stats': investor_stats,
            'llm_completion_text': llm_completion_text,
            'llm_completion_json': llm_completion_json,
            'llm_analyze_code': llm_analyze_code,
            'llm_generate_code': llm_generate_code,
            'llm_explain_concept': llm_explain_concept,
            'llm_debug_error': llm_debug_error,

        }
        
        print(f"✅ MCP Server pattern successful! Loaded {len(sprites)} sprite functions")
        
    except ImportError as e:
        print(f"❌ MCP Server pattern failed: {e}")
else:
    print("❌ Some imports failed - check individual results above")
