
from .spr_agf_agsearch import quick_search
#from .spr_tamarind import market_research
#from .spr_ext_search import web_search, news_search
from .spr_agf_omnisearch import omni_search
from .spr_ext_wiki import wiki_search
from .spr_agf_multimodel import multimodel_query
from .spr_agf_frames import frame_search, frame_list
from .spr_agf_investors import investor_search, investor_stats
from .spr_llm_smart import (
    llm_completion_text, llm_completion_json, llm_analyze_code,
    llm_generate_code, llm_explain_concept, llm_debug_error
)
from .spr_ext_deep_research import (
    deep_research_simple, deep_research_robust, deep_research_batch,
    deep_research_load, deep_research_list
)
from .spr_simple_chart import quick_chart

# Create namespace for sprite_l1 tools
class sprite_l1:
    """Namespace for sprite level 1 tools"""
    quick_search = quick_search
    omni_search = omni_search
    wiki_search = wiki_search
    multimodel_query = multimodel_query
    frame_search = frame_search
    frame_list = frame_list
    investor_search = investor_search
    investor_stats = investor_stats
    llm_completion_text = llm_completion_text
    llm_completion_json = llm_completion_json
    llm_analyze_code = llm_analyze_code
    llm_generate_code = llm_generate_code
    llm_explain_concept = llm_explain_concept
    llm_debug_error = llm_debug_error
    deep_research_simple = deep_research_simple
    deep_research_robust = deep_research_robust
    deep_research_batch = deep_research_batch
    deep_research_load = deep_research_load
    deep_research_list = deep_research_list
    quick_chart = quick_chart
